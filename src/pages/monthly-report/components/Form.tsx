import { useQuery } from '@tanstack/react-query'
import { Col, DatePicker, Form, Input, message, Row, Select, Spin } from 'antd'
import dayjs from 'dayjs'
import { useState } from 'react'

import { useAuth } from '@/contexts/auth.tsx'
import { type APIResponse, request } from '@/lib/request.ts'
import type { MonthlyReportDTO } from '@/universal/data-summary/types.ts'

export const MonthlyReportForm = () => {
  const { user } = useAuth()

  const [form] = Form.useForm()

  const [lastMonthData, setLastMonthData] = useState<MonthlyReportDTO>()
  const [lastYearData, setLastYearData] = useState<MonthlyReportDTO>()

  const getVerificationResult = (current: number, last: number | undefined) => {
    if (current === last) return null
    if (!last) return '较上月增加'
    return current > last ? '较上月增加' : '较上月减少'
  }

  const getLastMonthData = useQuery({
    queryKey: [
      user?.company_id,
      dayjs().subtract(1, 'month').format('YYYY-MM'),
    ],
    queryFn: async ({ queryKey: [company_id, period] }) => {
      const res = await request<APIResponse<{ Data: MonthlyReportDTO[] }>>(
        '/monthly-report/detail-by-periods',
        { query: { company_id, 'periods[]': period } },
      )
      if (res.code !== 200001) {
        message.error(res.message)
        return null
      }

      setLastMonthData(res.data.Data[0])
    },
    enabled: !!user?.company_id,
  })

  const getLastYearData = useQuery({
    queryKey: [user?.company_id, dayjs().subtract(1, 'year').format('YYYY-MM')],
    queryFn: async ({ queryKey: [company_id, period] }) => {
      const res = await request<APIResponse<{ Data: MonthlyReportDTO[] }>>(
        '/monthly-report/detail-by-periods',
        { query: { company_id, 'periods[]': period } },
      )
      if (res.code !== 200001) {
        message.error(res.message)
        return null
      }

      setLastYearData(res.data.Data[0])
    },
    enabled: !!user?.company_id,
  })

  const getDataUpToThisMonth = useQuery({
    queryKey: [user?.company_id],
    queryFn: async ({ queryKey: [company_id] }) => {
      const res = await request<APIResponse<MonthlyReportDTO>>(
        '/monthly-report/base-count',
        { query: { company_id } },
      )

      if (res.code !== 200001) {
        message.error(res.message)
        return null
      }

      form.setFieldsValue(res.data)
    },
    enabled: !!user?.company_id,
  })

  const watchThisMonthTotalAmount = Form.useWatch('total_amount', form)
  const watchThisMonthFixedAssetsAmount = Form.useWatch(
    'fixed_assets_amount',
    form,
  )
  const watchThisMonthRealEstateAmount = Form.useWatch(
    'real_estate_amount',
    form,
  )
  const watchThisMonthEquityInvestmentAmount = Form.useWatch(
    'equity_investment_amount',
    form,
  )
  const watchThisMonthForeignEquityAmount = Form.useWatch(
    'foreign_equity_amount',
    form,
  )
  const watchThisMonthMainBusinessAmount = Form.useWatch(
    'main_business_amount',
    form,
  )
  const watchThisMonthNonMainBusinessAmount = Form.useWatch(
    'non_main_business_amount',
    form,
  )
  const watchThisMonthDomesticAmount = Form.useWatch('domestic_amount', form)
  const watchThisMonthOverseasAmount = Form.useWatch('overseas_amount', form)
  const watchThisMonthKeyStrategicIndustryAmount = Form.useWatch(
    'key_strategic_industry_amount',
    form,
  )
  const watchThisMonthKeyFixedAssetsAmount = Form.useWatch(
    'key_fixed_assets_amount',
    form,
  )
  const watchThisMonthKeyEquityAmount = Form.useWatch('key_equity_amount', form)
  const watchThisMonthKeyForeignAmount = Form.useWatch(
    'key_foreign_amount',
    form,
  )
  const watchThisMonthKeyManufactureAmount = Form.useWatch(
    'key_manufacture_amount',
    form,
  )
  const watchThisMonthKeyMiningAmount = Form.useWatch('key_mining_amount', form)

  return (
    <Spin
      spinning={
        getDataUpToThisMonth.isLoading ||
        getLastMonthData.isLoading ||
        getLastYearData.isLoading
      }
    >
      <Form form={form}>
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              label="填报周期"
              name="period"
              rules={[{ required: true, message: '请选择填报周期' }]}
            >
              <DatePicker
                picker="month"
                className="w-full"
                placeholder="请选择填报周期"
                format="YYYY-MM"
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="编制单位"
              name="company_id"
              rules={[{ required: true, message: '请选择编制单位' }]}
            >
              <Select
                placeholder="请选择编制单位"
                options={[{ label: user?.company, value: user?.company_id }]}
                onChange={(_, option) => {
                  const selectedOption = Array.isArray(option)
                    ? option[0]
                    : option
                  form.setFieldValue('company_name', selectedOption?.label)
                }}
              />
            </Form.Item>
            <Form.Item name="company_name" noStyle />
          </Col>
        </Row>

        <Row>
          <table className="border-collapse border border-[#EAEAEA] [&_td]:border [&_td]:border-[#EAEAEA] [&_td]:px-1 [&_td]:text-center [&_td]:text-nowrap">
            <thead>
              <tr className="bg-[#E5EBFE] text-nowrap [&>th]:p-2 [&>th]:font-normal">
                <th colSpan={2}>类型</th>
                <th>截至本月数据(万元)</th>
                <th>截至上月数据(万元)</th>
                <th>去年同期(万元)</th>
                <th>校验结果</th>
                <th>备注说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td colSpan={2}>合计</td>
                <td>
                  <Form.Item
                    name="total_amount"
                    getValueFromEvent={(e) =>
                      e.target.value === '' ? null : Number(e.target.value)
                    }
                    rules={[{ required: true, message: '请输入合计' }]}
                  >
                    <Input type="number" />
                  </Form.Item>
                </td>
                <td>{lastMonthData?.total_amount}</td>
                <td>{lastYearData?.total_amount}</td>
                <td>
                  {getVerificationResult(
                    form.getFieldValue('total_amount'),
                    lastMonthData?.total_amount,
                  )}
                </td>
                <td>
                  <Form.Item name="total_amount_remarks" rules={}>
                    <Input placeholder="请输入" />
                  </Form.Item>
                </td>
              </tr>

              <tr>
                <td rowSpan={4}>投资方式</td>
                <td>固定资产</td>
                <td>
                  <Form.Item name="fixed_assets_amount">
                    <Input type="number" />
                  </Form.Item>
                </td>
                <td>{lastMonthData?.fixed_assets_amount}</td>
                <td>{lastYearData?.fixed_assets_amount}</td>
                <td>
                  {getVerificationResult(
                    form.getFieldValue('fixed_assets_amount'),
                    lastMonthData?.fixed_assets_amount,
                  )}
                </td>
                <td>
                  <Form.Item name="fixed_assets_amount_remark">
                    <Input placeholder="请输入" />
                  </Form.Item>
                </td>
              </tr>
              <tr>
                <td>其中:房地产</td>
                <td>
                  <Form.Item name="real_estate_amount">
                    <Input type="number" />
                  </Form.Item>
                </td>
                <td>{lastMonthData?.real_estate_amount}</td>
                <td>{lastYearData?.real_estate_amount}</td>
                <td>
                  {getVerificationResult(
                    form.getFieldValue('real_estate_amount'),
                    lastMonthData?.real_estate_amount,
                  )}
                </td>
                <td>
                  <Form.Item name="real_estate_amount_remark">
                    <Input placeholder="请输入" />
                  </Form.Item>
                </td>
              </tr>
              <tr>
                <td>股权投资</td>
                <td>
                  <Form.Item name="equity_investment_amount">
                    <Input />
                  </Form.Item>
                </td>
                <td>{lastMonthData?.equity_investment_amount}</td>
                <td>{lastYearData?.equity_investment_amount}</td>
                <td>
                  {getVerificationResult(
                    form.getFieldValue('equity_investment_amount'),
                    lastMonthData?.equity_investment_amount,
                  )}
                </td>
                <td>
                  <Form.Item name="equity_investment_amount_remark">
                    <Input placeholder="请输入" />
                  </Form.Item>
                </td>
              </tr>
              <tr>
                <td>其中:对外并购</td>
                <td>
                  <Form.Item name="foreign_equity_amount">
                    <Input />
                  </Form.Item>
                </td>
                <td>{lastMonthData?.foreign_equity_amount}</td>
                <td>{lastYearData?.foreign_equity_amount}</td>
                <td>
                  {getVerificationResult(
                    form.getFieldValue('foreign_equity_amount'),
                    lastMonthData?.foreign_equity_amount,
                  )}
                </td>
                <td>
                  <Form.Item name="foreign_equity_amount_remark">
                    <Input placeholder="请输入" />
                  </Form.Item>
                </td>
              </tr>
              <tr>
                <td rowSpan={2}>投资方向</td>
                <td>主业</td>
                <td>
                  <Form.Item name="main_business_amount">
                    <Input />
                  </Form.Item>
                </td>
                <td>{lastMonthData?.main_business_amount}</td>
                <td>{lastYearData?.main_business_amount}</td>
                <td>
                  {getVerificationResult(
                    form.getFieldValue('main_business_amount'),
                    lastMonthData?.main_business_amount,
                  )}
                </td>
                <td>
                  <Form.Item name="main_business_amount_remark">
                    <Input placeholder="请输入" />
                  </Form.Item>
                </td>
              </tr>
              <tr>
                <td>非主业</td>
                <td>
                  <Form.Item name="non_main_business_amount">
                    <Input />
                  </Form.Item>
                </td>
                <td>{lastMonthData?.non_main_business_amount}</td>
                <td>{lastYearData?.non_main_business_amount}</td>
                <td>
                  {getVerificationResult(
                    form.getFieldValue('non_main_business_amount'),
                    lastMonthData?.non_main_business_amount,
                  )}
                </td>
                <td>
                  <Form.Item name="non_main_business_amount_remark">
                    <Input placeholder="请输入" />
                  </Form.Item>
                </td>
              </tr>
              <tr>
                <td rowSpan={2}>投资区域</td>
                <td>境内</td>
                <td>
                  <Form.Item name="domestic_amount">
                    <Input />
                  </Form.Item>
                </td>
                <td>{lastMonthData?.domestic_amount}</td>
                <td>{lastYearData?.domestic_amount}</td>
                <td>
                  {getVerificationResult(
                    form.getFieldValue('domestic_amount'),
                    lastMonthData?.domestic_amount,
                  )}
                </td>
                <td>
                  <Form.Item name="domestic_amount_remark">
                    <Input placeholder="请输入" />
                  </Form.Item>
                </td>
              </tr>
              <tr>
                <td>境外</td>
                <td>
                  <Form.Item name="overseas_amount">
                    <Input />
                  </Form.Item>
                </td>
                <td>{lastMonthData?.overseas_amount}</td>
                <td>{lastYearData?.overseas_amount}</td>
                <td>
                  {getVerificationResult(
                    form.getFieldValue('overseas_amount'),
                    lastMonthData?.overseas_amount,
                  )}
                </td>
                <td>
                  <Form.Item name="overseas_amount_remark">
                    <Input placeholder="请输入" />
                  </Form.Item>
                </td>
              </tr>
              <tr>
                <td rowSpan={6}>重点领域</td>
                <td>战略性新兴产业</td>
                <td>
                  <Form.Item name="key_strategic_industry_amount">
                    <Input />
                  </Form.Item>
                </td>
                <td>{lastMonthData?.key_strategic_industry_amount}</td>
                <td>{lastYearData?.key_strategic_industry_amount}</td>
                <td>
                  {getVerificationResult(
                    form.getFieldValue('key_strategic_industry_amount'),
                    lastMonthData?.key_strategic_industry_amount,
                  )}
                </td>
                <td>
                  <Form.Item name="key_strategic_industry_amount_remark">
                    <Input placeholder="请输入" />
                  </Form.Item>
                </td>
              </tr>
              <tr>
                <td>其中:固定资产投资</td>
                <td>
                  <Form.Item name="key_fixed_assets_amount">
                    <Input />
                  </Form.Item>
                </td>
                <td>{lastMonthData?.key_fixed_assets_amount}</td>
                <td>{lastYearData?.key_fixed_assets_amount}</td>
                <td>
                  {getVerificationResult(
                    form.getFieldValue('key_fixed_assets_amount'),
                    lastMonthData?.key_fixed_assets_amount,
                  )}
                </td>
                <td>
                  <Form.Item name="fixed_assets_amount_remark">
                    <Input placeholder="请输入" />
                  </Form.Item>
                </td>
              </tr>
              <tr>
                <td>股权投资</td>
                <td>
                  <Form.Item name="key_equity_amount">
                    <Input />
                  </Form.Item>
                </td>
                <td>{lastMonthData?.key_equity_amount}</td>
                <td>{lastYearData?.key_equity_amount}</td>
                <td>
                  {getVerificationResult(
                    form.getFieldValue('key_equity_amount'),
                    lastMonthData?.key_equity_amount,
                  )}
                </td>
                <td>
                  <Form.Item name="key_equity_amount_remark">
                    <Input placeholder="请输入" />
                  </Form.Item>
                </td>
              </tr>
              <tr>
                <td>对外并购</td>
                <td>
                  <Form.Item name="key_foreign_amount">
                    <Input />
                  </Form.Item>
                </td>
                <td>{lastMonthData?.key_foreign_amount}</td>
                <td>{lastYearData?.key_foreign_amount}</td>
                <td>
                  {getVerificationResult(
                    form.getFieldValue('key_foreign_amount'),
                    lastMonthData?.key_foreign_amount,
                  )}
                </td>
                <td>
                  <Form.Item name="key_foreign_amount_remark">
                    <Input placeholder="请输入" />
                  </Form.Item>
                </td>
              </tr>
              <tr>
                <td>制造业</td>
                <td>
                  <Form.Item name="key_manufacture_amount">
                    <Input placeholder="请输入" />
                  </Form.Item>
                </td>
                <td>{lastMonthData?.key_manufacture_amount}</td>
                <td>{lastYearData?.key_manufacture_amount}</td>
                <td>
                  {getVerificationResult(
                    form.getFieldValue('key_manufacture_amount'),
                    lastMonthData?.key_manufacture_amount,
                  )}
                </td>
                <td>
                  <Form.Item name="key_manufacture_amount_remark">
                    <Input placeholder="请输入" />
                  </Form.Item>
                </td>
              </tr>
              <tr>
                <td>采矿业</td>
                <td>
                  <Form.Item name="key_mining_amount">
                    <Input placeholder="请输入" />
                  </Form.Item>
                </td>
                <td>{lastMonthData?.key_mining_amount}</td>
                <td>{lastYearData?.key_mining_amount}</td>
                <td>
                  {getVerificationResult(
                    form.getFieldValue('key_mining_amount'),
                    lastMonthData?.key_mining_amount,
                  )}
                </td>
                <td>
                  <Form.Item name="key_mining_amount_remark">
                    <Input placeholder="请输入" />
                  </Form.Item>
                </td>
              </tr>
              <tr>
                <td colSpan={2} style={{ textAlign: 'left', textWrap: 'wrap' }}>
                  月度分析(简述行业趋势分析及其对投资的影响、投资主要方向及变化、重大项目关键节点进展，150字以内)
                </td>
                <td colSpan={5}>
                  <Form.Item name="monthly_analysis">
                    <Input.TextArea autoSize={{ minRows: 3, maxRows: 5 }} />
                  </Form.Item>
                </td>
              </tr>
            </tbody>
          </table>
        </Row>
      </Form>
    </Spin>
  )
}
